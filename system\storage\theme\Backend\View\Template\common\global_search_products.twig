{% if products %}
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6 p-6">
        {% for product in products %}
        <!-- Product Card -->
            <div class="product-card bg-white rounded shadow hover:shadow-md transition-all">
                <div class="relative">
                    <a href="{{ product.edit }}">
                    <div class="product-image-container w-full h-48 rounded-t bg-gray-100 flex items-center justify-center relative" data-product-id="{{ product.product_id }}" data-width="400" data-height="300">
                        <div class="product-image-placeholder">
                            <div class="w-10 h-10 text-gray-300 animate-spin">
                                <i class="ri-loader-4-line ri-2x"></i>
                            </div>
                        </div>
                        <div class="product-image-error hidden">
                            <div class="w-10 h-10 text-red-500">
                                <i class="ri-error-warning-line ri-2x"></i>
                            </div>
                            <p class="text-sm text-red-500 mt-2">Грешка при зареждане</p>
                        </div>
                        <img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" alt="{{ product.name }}" class="w-full h-48 object-cover object-top rounded-t absolute inset-0 opacity-0 transition-opacity duration-300">
                    </div>
                    </a>
                    <div class="absolute top-3 right-3 flex space-x-2">
                        {% if product.status %}
                        <span class="px-2 py-1 bg-green-500 text-white text-xs rounded-full">Активен</span>
                        {% else %}
                        <span class="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full">Неактивен</span>
                        {% endif %}

                        {% if product.special %}
                        <span class="px-2 py-1 bg-red-500 text-white text-xs rounded-full">{{ product.discount_percent }}</span>
                        {% endif %}
                    </div>
                </div>
                <div class="p-4">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <h3 class="font-medium text-gray-800">{{ product.name }}</h3>
                            <p class="text-sm text-gray-500">{{ product.category }}</p>
                             <p class="font-bold text-gray-800">{{ product.price|number_format(2, '.', ',') }} лв.</p>
                            {% if product.special %}
                            <p class="text-sm text-gray-500 line-through">{{ product.old_price|number_format(2, '.', ',') }} лв.</p>
                            {% endif %}
                        </div>
                    </div>
                    <div class="flex items-center justify-between mt-4">
                        <span class="text-sm text-gray-500">Код: {{ product.model }}</span>
                        <div class="card-actions flex space-x-1">
                            <a href="{{ product.edit }}" class="w-8 h-8 flex items-center justify-center text-primary hover:bg-primary/10 rounded-full">
                                <div class="w-5 h-5 flex items-center justify-center">
                                    <i class="ri-edit-line"></i>
                                </div>
                            </a>
                              <div class="relative">
                                <button class="w-8 h-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 rounded-full product-actions-btn">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-more-2-line"></i>
                                    </div>
                                </button>
                                <div class="product-actions-dropdown hidden absolute right-0 bottom-full mb-2 bg-white border border-gray-200 rounded shadow-lg z-10 w-40">
                                    <ul class="py-1">
                                        <li>
                                            <a href="#" class="px-4 py-2 hover:bg-gray-100 flex items-center text-sm duplicate-product" data-product-id="{{ product.product_id }}">
                                                <i class="ri-file-copy-line mr-2"></i> Дублиране
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#" class="px-4 py-2 hover:bg-gray-100 flex items-center text-sm delete-product" data-product-id="{{ product.product_id }}">
                                                <i class="ri-delete-bin-5-line text-red-500 mr-2"></i> Изтриване
                                            </a>
                                        </li>

                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-8">
        <i class="ri-box-line text-gray-400 text-4xl mb-4"></i>
        <p class="text-gray-500">Няма намерени продукти</p>
    </div>
{% endif %}
